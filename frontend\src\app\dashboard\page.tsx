'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { reportsAPI, activitiesAPI, challengesAPI, emotionAPI, mockData, isDemoMode } from '@/utils/api';
import type { DailySummary, Activity, Challenge } from '@/utils/api';
import ConnectionStatus from '@/components/ConnectionStatus';
import ThemeToggle from '@/components/ThemeToggle';
import AnimatedCard from '@/components/AnimatedCard';

export default function Dashboard() {
  const { user, isAuthenticated, logout } = useAuth();
  const { theme } = useTheme();
  const router = useRouter();
  const [dailySummary, setDailySummary] = useState<DailySummary | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [weeklyTrend, setWeeklyTrend] = useState<DailySummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }

    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // Check if we're in demo mode
        if (isDemoMode()) {
          // Use mock data for demo
          setDailySummary(mockData.dailySummary);
          setActivities(mockData.activities.slice(0, 3));
          setChallenges(mockData.challenges.slice(0, 2));
          setWeeklyTrend(mockData.weeklyTrend);
        } else {
          // Fetch all dashboard data in parallel from API
          const [summaryRes, activitiesRes, challengesRes, trendRes] = await Promise.all([
            reportsAPI.getDailySummary(),
            activitiesAPI.getSuggestions(),
            challengesAPI.getActive(),
            reportsAPI.getWeeklyTrend()
          ]);

          setDailySummary(summaryRes);
          setActivities(activitiesRes.activities.slice(0, 3)); // Show top 3 activities
          setChallenges(challengesRes.slice(0, 2)); // Show top 2 challenges
          setWeeklyTrend(trendRes.weekly_trend);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Fallback to mock data if API fails
        setDailySummary(mockData.dailySummary);
        setActivities(mockData.activities.slice(0, 3));
        setChallenges(mockData.challenges.slice(0, 2));
        setWeeklyTrend(mockData.weeklyTrend);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthenticated, router]);

  const handleLogout = () => {
    logout();
    router.push('/auth');
  };

  const getEmotionColor = (emotion: string) => {
    const colors: Record<string, string> = {
      positive: 'text-green-600 bg-green-100',
      negative: 'text-red-600 bg-red-100',
      neutral: 'text-gray-600 bg-gray-100',
    };
    return colors[emotion] || 'text-gray-600 bg-gray-100';
  };

  const getScoreColor = (score: number) => {
    if (score >= 70) return 'text-green-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className={`text-3xl font-bold transition-colors duration-300 ${
                theme === 'dark'
                  ? 'bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent'
              }`}>
                🧠 AI Driven Emotion Analysis
              </h1>
              <p className={`transition-colors duration-300 ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
                Welcome back, {user?.name}! Your intelligent wellness dashboard
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <ConnectionStatus />
              <ThemeToggle showLabel={false} />
              <nav className="flex space-x-4">
                <button
                  onClick={() => router.push('/reports')}
                  className="text-gray-500 hover:text-gray-700 text-sm font-medium"
                >
                  Reports
                </button>
                <button
                  onClick={() => router.push('/challenges')}
                  className="text-gray-500 hover:text-gray-700 text-sm font-medium"
                >
                  Challenges
                </button>
                <button
                  onClick={() => router.push('/profile')}
                  className="text-gray-500 hover:text-gray-700 text-sm font-medium"
                >
                  Profile
                </button>
              </nav>
              <div className="flex space-x-2">
                <button
                  onClick={() => router.push('/emotion-input')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                >
                  <span>📝</span>
                  <span>Text</span>
                </button>
                <button
                  onClick={() => router.push('/emotion-input')}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-1"
                >
                  <span>📷</span>
                  <span>Image</span>
                </button>
              </div>
              <button
                onClick={handleLogout}
                className="text-gray-500 hover:text-gray-700 text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Today's Summary */}
          <AnimatedCard className="mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className={`text-lg leading-6 font-medium mb-4 flex items-center space-x-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                <span className="text-2xl">📊</span>
                <span>Today's AI Emotion Analysis</span>
              </h3>
            {dailySummary ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className={`text-3xl font-bold ${getScoreColor(dailySummary.emotion_score)}`}>
                    {dailySummary.emotion_score}/100
                  </div>
                  <div className="text-sm text-gray-500">Emotion Score</div>
                </div>
                <div className="text-center">
                  <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getEmotionColor(dailySummary.dominant_emotion)}`}>
                    {dailySummary.dominant_emotion}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">Dominant Emotion</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900">
                    {dailySummary.total_logs}
                  </div>
                  <div className="text-sm text-gray-500">Logs Today</div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500">
                No emotion data for today. Start by detecting your emotions!
              </div>
            )}
            </div>
          </AnimatedCard>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <button
              onClick={() => router.push('/emotion-input')}
              className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center"
            >
              <div className="text-2xl mb-2">🎯</div>
              <div className="font-medium">Detect Emotion</div>
            </button>
            <button
              onClick={() => router.push('/diary')}
              className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center"
            >
              <div className="text-2xl mb-2">📝</div>
              <div className="font-medium">Emotion Diary</div>
            </button>
            <button
              onClick={() => router.push('/reports')}
              className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center"
            >
              <div className="text-2xl mb-2">📊</div>
              <div className="font-medium">View Reports</div>
            </button>
            <button
              onClick={() => router.push('/challenges')}
              className="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center"
            >
              <div className="text-2xl mb-2">🏆</div>
              <div className="font-medium">Challenges</div>
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Suggestions */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Suggested Activities
                </h3>
                {activities.length > 0 ? (
                  <div className="space-y-3">
                    {activities.map((activity) => (
                      <div key={activity.id} className="border-l-4 border-blue-400 pl-4">
                        <div className="text-sm font-medium text-gray-900">
                          {activity.activity_description}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {activity.category} • {activity.duration_minutes} min • {activity.difficulty_level}
                        </div>
                        {activity.link && (
                          <a
                            href={activity.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-xs"
                          >
                            Learn more →
                          </a>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm">
                    No activity suggestions available. Try detecting your emotions first!
                  </div>
                )}
                <div className="mt-4">
                  <button
                    onClick={() => router.push('/activities')}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View all activities →
                  </button>
                </div>
              </div>
            </div>

            {/* Active Challenges */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Active Challenges
                </h3>
                {challenges.length > 0 ? (
                  <div className="space-y-4">
                    {challenges.map((challenge) => (
                      <div key={challenge.id} className="border rounded-lg p-3">
                        <div className="flex justify-between items-start mb-2">
                          <div className="text-sm font-medium text-gray-900">
                            {challenge.title}
                          </div>
                          <div className="text-xs text-gray-500">
                            {challenge.completed_days}/{challenge.target_days} days
                          </div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${challenge.progress}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {challenge.progress}% complete
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm">
                    No active challenges. Start a new challenge to improve your well-being!
                  </div>
                )}
                <div className="mt-4">
                  <button
                    onClick={() => router.push('/challenges')}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View all challenges →
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Weekly Trend Preview */}
          {weeklyTrend.length > 0 && (
            <div className="bg-white overflow-hidden shadow rounded-lg mt-6">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Weekly Emotion Trend
                </h3>
                <div className="grid grid-cols-7 gap-2">
                  {weeklyTrend.map((day, index) => (
                    <div key={index} className="text-center">
                      <div className="text-xs text-gray-500 mb-1">
                        {new Date(day.date).toLocaleDateString('en', { weekday: 'short' })}
                      </div>
                      <div className={`w-8 h-8 mx-auto rounded-full flex items-center justify-center text-xs font-medium ${getEmotionColor(day.dominant_emotion)}`}>
                        {Math.round(day.emotion_score)}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => router.push('/reports')}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View detailed reports →
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
