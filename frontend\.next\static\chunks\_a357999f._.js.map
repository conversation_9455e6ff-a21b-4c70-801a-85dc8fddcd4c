{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/src/app/emotion-input/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { emotionAPI, activitiesAPI, isDemoMode } from '@/utils/api';\nimport type { EmotionResult, Activity } from '@/utils/api';\n\nexport default function EmotionInputPage() {\n  const { isAuthenticated } = useAuth();\n  const router = useRouter();\n  const [inputType, setInputType] = useState<'text' | 'image'>('text');\n  const [text, setText] = useState('');\n  const [selectedImage, setSelectedImage] = useState<File | null>(null);\n  const [imagePreview, setImagePreview] = useState<string | null>(null);\n  const [result, setResult] = useState<EmotionResult | null>(null);\n  const [activities, setActivities] = useState<Activity[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      if (!file.type.startsWith('image/')) {\n        setError('Please select a valid image file');\n        return;\n      }\n\n      if (file.size > 5 * 1024 * 1024) { // 5MB limit\n        setError('Image size must be less than 5MB');\n        return;\n      }\n\n      setSelectedImage(file);\n      setError('');\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setImagePreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (inputType === 'text' && !text.trim()) {\n      setError('Please enter some text to analyze');\n      return;\n    }\n\n    if (inputType === 'image' && !selectedImage) {\n      setError('Please select an image to analyze');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      let emotionResult: EmotionResult;\n\n      if (isDemoMode()) {\n        // Generate mock emotion result for demo\n        const mockEmotions = ['happy', 'sad', 'angry', 'fear', 'surprise', 'neutral'];\n        const primaryEmotion = mockEmotions[Math.floor(Math.random() * mockEmotions.length)];\n\n        emotionResult = {\n          text: inputType === 'text' ? text.trim() : `Image: ${selectedImage?.name}`,\n          primary_label: primaryEmotion,\n          confidence: Math.random() * 0.3 + 0.7, // 0.7 to 1.0\n          detected_emotions: {\n            [primaryEmotion]: Math.random() * 0.3 + 0.7,\n            [mockEmotions[Math.floor(Math.random() * mockEmotions.length)]]: Math.random() * 0.3 + 0.2,\n            [mockEmotions[Math.floor(Math.random() * mockEmotions.length)]]: Math.random() * 0.2 + 0.1,\n          },\n          timestamp: new Date().toISOString(),\n          user_id: 1,\n          input_type: inputType\n        };\n      } else {\n        // Real API calls\n        if (inputType === 'text') {\n          emotionResult = await emotionAPI.detectEmotion(text.trim());\n        } else {\n          // Handle image emotion detection\n          if (selectedImage) {\n            emotionResult = await emotionAPI.detectEmotionFromImage(selectedImage);\n          } else {\n            throw new Error('No image selected');\n          }\n        }\n      }\n\n      setResult(emotionResult);\n\n      // Get activity suggestions based on detected emotion\n      if (isDemoMode()) {\n        const mockActivities: Activity[] = [\n          {\n            id: 1,\n            emotion_category: 'general',\n            activity_description: inputType === 'image'\n              ? 'Try some facial exercises to improve your mood'\n              : 'Take a few deep breaths and practice mindfulness',\n            link: null,\n            category: 'mindfulness',\n            duration_minutes: 5,\n            difficulty_level: 'easy'\n          },\n          {\n            id: 2,\n            emotion_category: 'general',\n            activity_description: inputType === 'image'\n              ? 'Look in the mirror and practice positive affirmations'\n              : 'Go for a short walk outside',\n            link: null,\n            category: inputType === 'image' ? 'self-care' : 'exercise',\n            duration_minutes: 15,\n            difficulty_level: 'easy'\n          }\n        ];\n        setActivities(mockActivities);\n      } else {\n        const activitySuggestions = await activitiesAPI.getSuggestions(emotionResult.primary_label);\n        setActivities(activitySuggestions.activities.slice(0, 5));\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.detail || `Failed to analyze emotion from ${inputType}`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClear = () => {\n    setText('');\n    setSelectedImage(null);\n    setImagePreview(null);\n    setResult(null);\n    setActivities([]);\n    setError('');\n  };\n\n  const getEmotionColor = (label: string) => {\n    const colors: Record<string, string> = {\n      positive: 'text-green-600 bg-green-100 border-green-200',\n      negative: 'text-red-600 bg-red-100 border-red-200',\n      neutral: 'text-gray-600 bg-gray-100 border-gray-200',\n    };\n    return colors[label] || 'text-gray-600 bg-gray-100 border-gray-200';\n  };\n\n  const getTopEmotions = (emotions: Record<string, number>) => {\n    return Object.entries(emotions)\n      .sort(([, a], [, b]) => b - a)\n      .slice(0, 5);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Emotion Detection</h1>\n              <p className=\"text-gray-600\">Analyze your emotions from text</p>\n            </div>\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n            >\n              ← Back to Dashboard\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Input Type Selector */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                Choose Detection Method\n              </h3>\n              <div className=\"flex space-x-4 mb-6\">\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setInputType('text');\n                    setError('');\n                  }}\n                  className={`flex-1 py-3 px-4 rounded-lg border-2 transition-colors ${\n                    inputType === 'text'\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'\n                  }`}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-2\">📝</div>\n                    <div className=\"font-medium\">Text Analysis</div>\n                    <div className=\"text-sm opacity-75\">Analyze emotions from written text</div>\n                  </div>\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setInputType('image');\n                    setError('');\n                  }}\n                  className={`flex-1 py-3 px-4 rounded-lg border-2 transition-colors ${\n                    inputType === 'image'\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'\n                  }`}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-2\">📷</div>\n                    <div className=\"font-medium\">Image Analysis</div>\n                    <div className=\"text-sm opacity-75\">Detect emotions from facial expressions</div>\n                  </div>\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Input Form */}\n          <div className=\"bg-white shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <form onSubmit={handleSubmit}>\n                {inputType === 'text' ? (\n                  <div className=\"mb-4\">\n                    <label htmlFor=\"emotion-text\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      How are you feeling? Describe your emotions in text:\n                    </label>\n                    <textarea\n                      id=\"emotion-text\"\n                      value={text}\n                      onChange={(e) => setText(e.target.value)}\n                      rows={4}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                      placeholder=\"e.g., I'm feeling really happy today because I accomplished my goals...\"\n                      disabled={isLoading}\n                    />\n                  </div>\n                ) : (\n                  <div className=\"mb-4\">\n                    <label htmlFor=\"emotion-image\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Upload an image to analyze facial emotions:\n                    </label>\n                    <div className=\"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md\">\n                      <div className=\"space-y-1 text-center\">\n                        {imagePreview ? (\n                          <div className=\"mb-4\">\n                            <img\n                              src={imagePreview}\n                              alt=\"Preview\"\n                              className=\"mx-auto h-32 w-32 object-cover rounded-lg\"\n                            />\n                            <button\n                              type=\"button\"\n                              onClick={() => {\n                                setSelectedImage(null);\n                                setImagePreview(null);\n                              }}\n                              className=\"mt-2 text-sm text-red-600 hover:text-red-800\"\n                            >\n                              Remove image\n                            </button>\n                          </div>\n                        ) : (\n                          <>\n                            <svg\n                              className=\"mx-auto h-12 w-12 text-gray-400\"\n                              stroke=\"currentColor\"\n                              fill=\"none\"\n                              viewBox=\"0 0 48 48\"\n                            >\n                              <path\n                                d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\n                                strokeWidth={2}\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                              />\n                            </svg>\n                            <div className=\"flex text-sm text-gray-600\">\n                              <label\n                                htmlFor=\"emotion-image\"\n                                className=\"relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500\"\n                              >\n                                <span>Upload a file</span>\n                                <input\n                                  id=\"emotion-image\"\n                                  name=\"emotion-image\"\n                                  type=\"file\"\n                                  accept=\"image/*\"\n                                  className=\"sr-only\"\n                                  onChange={handleImageSelect}\n                                  disabled={isLoading}\n                                />\n                              </label>\n                              <p className=\"pl-1\">or drag and drop</p>\n                            </div>\n                            <p className=\"text-xs text-gray-500\">PNG, JPG, GIF up to 5MB</p>\n                          </>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {error && (\n                  <div className=\"mb-4 rounded-md bg-red-50 p-4\">\n                    <div className=\"text-sm text-red-700\">{error}</div>\n                  </div>\n                )}\n\n                <div className=\"flex space-x-3\">\n                  <button\n                    type=\"submit\"\n                    disabled={isLoading || !text.trim()}\n                    className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium disabled:cursor-not-allowed\"\n                  >\n                    {isLoading ? (\n                      <div className=\"flex items-center\">\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                        Analyzing...\n                      </div>\n                    ) : (\n                      'Analyze Emotion'\n                    )}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={handleClear}\n                    className=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Clear\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n\n          {/* Results */}\n          {result && (\n            <div className=\"space-y-6\">\n              {/* Primary Emotion */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Primary Emotion</h3>\n                  <div className=\"flex items-center justify-between\">\n                    <div className={`inline-flex px-4 py-2 rounded-lg border ${getEmotionColor(result.primary_label)}`}>\n                      <span className=\"text-lg font-semibold capitalize\">{result.primary_label}</span>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-gray-900\">\n                        {Math.round(result.confidence_score * 100)}%\n                      </div>\n                      <div className=\"text-sm text-gray-500\">Confidence</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Detailed Emotions */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Detailed Emotion Analysis</h3>\n                  <div className=\"space-y-3\">\n                    {getTopEmotions(result.emotions).map(([emotion, score]) => (\n                      <div key={emotion} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <span className=\"text-sm font-medium text-gray-900 capitalize w-24\">\n                            {emotion}\n                          </span>\n                          <div className=\"flex-1 mx-4\">\n                            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                              <div\n                                className=\"bg-blue-600 h-2 rounded-full\"\n                                style={{ width: `${score * 100}%` }}\n                              ></div>\n                            </div>\n                          </div>\n                        </div>\n                        <span className=\"text-sm text-gray-600 w-12 text-right\">\n                          {Math.round(score * 100)}%\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Activity Suggestions */}\n              {activities.length > 0 && (\n                <div className=\"bg-white shadow rounded-lg\">\n                  <div className=\"px-4 py-5 sm:p-6\">\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                      Suggested Activities for {result.primary_label} emotions\n                    </h3>\n                    <div className=\"space-y-4\">\n                      {activities.map((activity) => (\n                        <div key={activity.id} className=\"border-l-4 border-blue-400 pl-4 py-2\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {activity.activity_description}\n                          </div>\n                          <div className=\"text-xs text-gray-500 mt-1\">\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2\">\n                              {activity.category}\n                            </span>\n                            {activity.duration_minutes && (\n                              <span className=\"mr-2\">{activity.duration_minutes} min</span>\n                            )}\n                            <span className=\"capitalize\">{activity.difficulty_level}</span>\n                          </div>\n                          {activity.link && (\n                            <a\n                              href={activity.link}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"text-blue-600 hover:text-blue-800 text-xs mt-1 inline-block\"\n                            >\n                              Learn more →\n                            </a>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Action Buttons */}\n              <div className=\"flex space-x-4\">\n                <button\n                  onClick={() => router.push('/diary')}\n                  className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Add to Diary\n                </button>\n                <button\n                  onClick={() => router.push('/dashboard')}\n                  className=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Back to Dashboard\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;YACd;QACF;qCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,oBAAoB,CAAC;YACZ;QAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,SAAS;gBACT;YACF;YAEA,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,SAAS;gBACT;YACF;YAEA,iBAAiB;YACjB,SAAS;YAET,iBAAiB;YACjB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;oBACC;gBAAhB,iBAAgB,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;YAClC;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,cAAc,UAAU,CAAC,KAAK,IAAI,IAAI;YACxC,SAAS;YACT;QACF;QAEA,IAAI,cAAc,WAAW,CAAC,eAAe;YAC3C,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,IAAI;YAEJ,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;gBAChB,wCAAwC;gBACxC,MAAM,eAAe;oBAAC;oBAAS;oBAAO;oBAAS;oBAAQ;oBAAY;iBAAU;gBAC7E,MAAM,iBAAiB,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;gBAEpF,gBAAgB;oBACd,MAAM,cAAc,SAAS,KAAK,IAAI,KAAK,AAAC,UAA6B,OAApB,0BAAA,oCAAA,cAAe,IAAI;oBACxE,eAAe;oBACf,YAAY,KAAK,MAAM,KAAK,MAAM;oBAClC,mBAAmB;wBACjB,CAAC,eAAe,EAAE,KAAK,MAAM,KAAK,MAAM;wBACxC,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE,CAAC,EAAE,KAAK,MAAM,KAAK,MAAM;wBACvF,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE,CAAC,EAAE,KAAK,MAAM,KAAK,MAAM;oBACzF;oBACA,WAAW,IAAI,OAAO,WAAW;oBACjC,SAAS;oBACT,YAAY;gBACd;YACF,OAAO;gBACL,iBAAiB;gBACjB,IAAI,cAAc,QAAQ;oBACxB,gBAAgB,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,KAAK,IAAI;gBAC1D,OAAO;oBACL,iCAAiC;oBACjC,IAAI,eAAe;wBACjB,gBAAgB,MAAM,sHAAA,CAAA,aAAU,CAAC,sBAAsB,CAAC;oBAC1D,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF;YACF;YAEA,UAAU;YAEV,qDAAqD;YACrD,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,KAAK;gBAChB,MAAM,iBAA6B;oBACjC;wBACE,IAAI;wBACJ,kBAAkB;wBAClB,sBAAsB,cAAc,UAChC,mDACA;wBACJ,MAAM;wBACN,UAAU;wBACV,kBAAkB;wBAClB,kBAAkB;oBACpB;oBACA;wBACE,IAAI;wBACJ,kBAAkB;wBAClB,sBAAsB,cAAc,UAChC,0DACA;wBACJ,MAAM;wBACN,UAAU,cAAc,UAAU,cAAc;wBAChD,kBAAkB;wBAClB,kBAAkB;oBACpB;iBACD;gBACD,cAAc;YAChB,OAAO;gBACL,MAAM,sBAAsB,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC,cAAc,aAAa;gBAC1F,cAAc,oBAAoB,UAAU,CAAC,KAAK,CAAC,GAAG;YACxD;QACF,EAAE,OAAO,KAAU;gBACR,oBAAA;YAAT,SAAS,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI,AAAC,kCAA2C,OAAV;QAC3E,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,iBAAiB;QACjB,gBAAgB;QAChB,UAAU;QACV,cAAc,EAAE;QAChB,SAAS;IACX;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAiC;YACrC,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,MAAM,IAAI;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,OAAO,OAAO,CAAC,UACnB,IAAI,CAAC;gBAAC,GAAG,EAAE,UAAE,GAAG,EAAE;mBAAK,IAAI;WAC3B,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS;oDACP,aAAa;oDACb,SAAS;gDACX;gDACA,WAAW,AAAC,0DAIX,OAHC,cAAc,SACV,6CACA;0DAGN,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;sEAAqB;;;;;;;;;;;;;;;;;0DAGxC,6LAAC;gDACC,MAAK;gDACL,SAAS;oDACP,aAAa;oDACb,SAAS;gDACX;gDACA,WAAW,AAAC,0DAIX,OAHC,cAAc,UACV,6CACA;0DAGN,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;sEAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,UAAU;;wCACb,cAAc,uBACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAA+C;;;;;;8DAGvF,6LAAC;oDACC,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,MAAM;oDACN,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;iEAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAA+C;;;;;;8DAGxF,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,6BACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,KAAK;oEACL,KAAI;oEACJ,WAAU;;;;;;8EAEZ,6LAAC;oEACC,MAAK;oEACL,SAAS;wEACP,iBAAiB;wEACjB,gBAAgB;oEAClB;oEACA,WAAU;8EACX;;;;;;;;;;;iFAKH;;8EACE,6LAAC;oEACC,WAAU;oEACV,QAAO;oEACP,MAAK;oEACL,SAAQ;8EAER,cAAA,6LAAC;wEACC,GAAE;wEACF,aAAa;wEACb,eAAc;wEACd,gBAAe;;;;;;;;;;;8EAGnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,SAAQ;4EACR,WAAU;;8FAEV,6LAAC;8FAAK;;;;;;8FACN,6LAAC;oFACC,IAAG;oFACH,MAAK;oFACL,MAAK;oFACL,QAAO;oFACP,WAAU;oFACV,UAAU;oFACV,UAAU;;;;;;;;;;;;sFAGd,6LAAC;4EAAE,WAAU;sFAAO;;;;;;;;;;;;8EAEtB,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;wCAQhD,uBACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;sDAI3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,UAAU,aAAa,CAAC,KAAK,IAAI;oDACjC,WAAU;8DAET,0BACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;4DAAuE;;;;;;+DAIxF;;;;;;8DAGJ,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASR,wBACC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,2CAAgF,OAAtC,gBAAgB,OAAO,aAAa;kEAC7F,cAAA,6LAAC;4DAAK,WAAU;sEAAoC,OAAO,aAAa;;;;;;;;;;;kEAE1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,KAAK,CAAC,OAAO,gBAAgB,GAAG;oEAAK;;;;;;;0EAE7C,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;0DACZ,eAAe,OAAO,QAAQ,EAAE,GAAG,CAAC;wDAAC,CAAC,SAAS,MAAM;yEACpD,6LAAC;wDAAkB,WAAU;;0EAC3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb;;;;;;kFAEH,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFACC,WAAU;gFACV,OAAO;oFAAE,OAAO,AAAC,GAAc,OAAZ,QAAQ,KAAI;gFAAG;;;;;;;;;;;;;;;;;;;;;;0EAK1C,6LAAC;gEAAK,WAAU;;oEACb,KAAK,KAAK,CAAC,QAAQ;oEAAK;;;;;;;;uDAfnB;;;;;;;;;;;;;;;;;;;;;;gCAwBjB,WAAW,MAAM,GAAG,mBACnB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAyC;oDAC3B,OAAO,aAAa;oDAAC;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAI,WAAU;0EACZ,SAAS,oBAAoB;;;;;;0EAEhC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb,SAAS,QAAQ;;;;;;oEAEnB,SAAS,gBAAgB,kBACxB,6LAAC;wEAAK,WAAU;;4EAAQ,SAAS,gBAAgB;4EAAC;;;;;;;kFAEpD,6LAAC;wEAAK,WAAU;kFAAc,SAAS,gBAAgB;;;;;;;;;;;;4DAExD,SAAS,IAAI,kBACZ,6LAAC;gEACC,MAAM,SAAS,IAAI;gEACnB,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;uDAnBK,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;8CA+B/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAxcwB;;QACM,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/final%20year%20project/whole%20project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}